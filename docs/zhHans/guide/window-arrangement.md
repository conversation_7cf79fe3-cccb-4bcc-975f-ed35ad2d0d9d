---
title: 窗口编排
---

# 设备窗口编排功能详解

设备窗口编排是 Escrcpy 的高级功能之一，专为多设备管理和屏幕空间优化而设计。通过可视化的拖拽界面，您可以精确控制每个设备窗口的位置、大小和布局，实现高效的多设备协同操作。

## 功能概述

窗口编排功能提供了一个全屏的可视化编辑界面，让您能够：

- 同时管理多个设备窗口的布局
- 通过拖拽方式调整窗口位置和尺寸
- 设置全局默认配置和设备特定配置
- 实时预览窗口布局效果
- 保存和加载自定义布局方案

## 核心组件

### 全局配置组件
全局配置组件用于设置所有设备的默认窗口参数，包括：
- 默认窗口宽度和高度
- 默认窗口位置坐标
- 其他通用窗口属性

### 设备窗口组件
每个连接的设备都可以添加为独立的窗口组件，支持：
- 独立的位置和尺寸设置
- 设备特定的窗口配置
- 继承或覆盖全局配置

## 详细操作指南

### 启动窗口编排

1. **通过设备列表启动**
   - 在主界面的设备列表中找到任意设备
   - 点击设备操作栏中的"窗口编排"按钮
   - 系统将打开全屏的窗口编排界面

2. **通过快捷栏启动**
   - 使用顶部快捷操作栏中的窗口编排功能
   - 直接进入编排模式

### 界面布局说明

窗口编排界面采用全屏设计，主要包含以下区域：

#### 控制面板（顶部）
- **添加组件按钮**：下拉菜单形式，可选择添加全局配置或特定设备
- **重置布局按钮**：恢复到默认的窗口布局设置
- **清除全部按钮**：移除所有已添加的窗口组件

#### 编排区域（主体）
- 带虚线边框的可视化编排空间
- 所有窗口组件都在此区域内进行布局
- 支持实时拖拽和调整操作

#### 操作按钮（底部）
- **取消按钮**：放弃当前修改，关闭编排界面
- **保存布局按钮**：保存当前布局配置并应用

### 添加和管理组件

#### 添加全局配置组件

1. 点击"添加组件"下拉按钮
2. 选择"全局配置"选项
3. 全局配置组件将出现在编排区域
4. 该组件用于设置所有设备的默认参数

**注意**：每个编排方案中只能有一个全局配置组件。

#### 添加设备窗口组件

1. 点击"添加组件"下拉按钮
2. 从设备列表中选择要添加的设备
3. 设备窗口组件将添加到编排区域
4. 每个设备只能添加一次

**设备显示规则**：
- 只显示当前已连接的设备
- 已添加的设备不会在下拉列表中重复显示
- 设备名称优先显示自定义名称，其次为设备型号

### 窗口布局调整

#### 移动窗口位置

1. **选择目标窗口**：点击要移动的窗口组件
2. **拖拽移动**：按住鼠标左键拖拽窗口到目标位置
3. **实时反馈**：拖拽过程中可以看到实时的位置变化
4. **释放定位**：松开鼠标完成位置调整

#### 调整窗口尺寸

1. **定位调整点**：将鼠标移动到窗口的边角位置
2. **拖拽调整**：按住鼠标左键拖拽来改变窗口大小
3. **比例约束**：系统会自动维持合理的窗口比例
4. **最小尺寸限制**：窗口不能小于预设的最小尺寸

#### 尺寸限制说明

- **最小宽度**：容器宽度的 1/6
- **最小高度**：容器高度的 1/4
- **边界约束**：窗口不能拖拽到编排区域外部
- **重叠处理**：允许窗口重叠，但建议避免完全遮挡

### 布局管理操作

#### 重置布局

1. 点击控制面板中的"重置布局"按钮
2. 系统将清除当前所有组件
3. 重新加载保存的布局配置
4. 如果没有保存的配置，将显示空白编排区域

#### 清除全部组件

1. 点击"清除全部"按钮
2. 系统将弹出确认对话框
3. 确认后将移除所有窗口组件
4. 编排区域将变为空白状态

#### 删除单个组件

1. 将鼠标悬停在目标窗口组件上
2. 点击窗口右上角的删除按钮（×）
3. 该组件将立即从编排区域移除
4. 对应的设备将重新出现在可添加列表中

## 配置保存和应用

### 保存布局配置

1. **完成布局调整**：确保所有窗口组件都已调整到满意的位置和尺寸
2. **点击保存按钮**：点击界面底部的"保存布局"按钮
3. **配置写入**：系统将当前布局信息写入配置文件
4. **成功反馈**：显示保存成功的消息提示

### 配置存储机制

- **全局配置**：存储在 `scrcpy.global` 配置节点
- **设备配置**：存储在 `scrcpy.[设备ID]` 配置节点
- **参数格式**：
  - `--window-width`：窗口宽度
  - `--window-height`：窗口高度
  - `--window-x`：窗口X坐标
  - `--window-y`：窗口Y坐标

### 配置应用时机

- **启动镜像时**：设备开始镜像时自动应用对应的窗口配置
- **配置继承**：设备特定配置优先，未设置的参数继承全局配置
- **动态更新**：保存配置后立即生效，无需重启应用

## 最佳实践建议

### 屏幕空间规划

1. **评估显示器尺寸**：根据显示器分辨率合理规划窗口数量
2. **避免过度重叠**：确保重要的设备窗口不被完全遮挡
3. **预留操作空间**：为鼠标操作和界面切换预留足够空间
4. **考虑使用频率**：将常用设备放在更容易访问的位置

### 多设备协同

1. **逻辑分组**：将相关功能的设备窗口放置在相邻位置
2. **尺寸统一**：同类型设备使用相似的窗口尺寸
3. **布局对称**：采用对称或规律性的布局提高视觉效果
4. **功能区分**：通过位置区分不同用途的设备窗口

### 配置管理

1. **定期备份**：重要的布局配置建议定期备份
2. **场景切换**：为不同的工作场景准备不同的布局方案
3. **渐进调整**：从简单布局开始，逐步优化到理想状态
4. **团队共享**：在团队环境中可以共享优秀的布局配置

## 故障排除

### 常见问题

**问题1：设备不显示在添加列表中**
- 确认设备已正确连接
- 检查设备是否已经添加到编排中
- 尝试刷新设备列表

**问题2：窗口拖拽不响应**
- 确认鼠标点击在窗口的可拖拽区域
- 检查是否有其他程序占用鼠标事件
- 尝试重新打开编排界面

**问题3：保存的布局没有生效**
- 确认点击了"保存布局"按钮
- 检查配置文件是否有写入权限
- 尝试重启应用后再次测试

**问题4：窗口尺寸异常**
- 检查是否超出了最小尺寸限制
- 确认显示器分辨率设置正确
- 尝试重置布局后重新配置

### 性能优化

1. **限制窗口数量**：避免同时打开过多设备窗口
2. **合理设置尺寸**：过大的窗口可能影响性能
3. **定期清理**：清理不再使用的设备配置
4. **监控资源使用**：注意CPU和内存使用情况

通过合理使用窗口编排功能，您可以显著提高多设备管理的效率，创建适合自己工作流程的个性化操作环境。
