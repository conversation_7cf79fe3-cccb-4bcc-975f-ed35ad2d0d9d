---  
title: 操作指南  
---

# 设备操作指南  

## 批量处理  

- 批量镜像  
- 批量截屏  
- 批量安装应用  
- 批量文件管理  
- 批量执行脚本  
- 批量计划任务  

## 控制模式  

- 镜像模式  
- 录制模式  
- 摄像头录制  
- 音频录制  
- 摄像头  
- 自定义模式  
- OTG模式  

## 设备交互栏

- 切换应用
- 返回主页
- 返回键
- 启动APP（镜像组）
- 关闭屏幕（实验性功能）
- 通知中心
- 电源键
- 屏幕旋转
- 音量控制
- 截图
- 重启设备
- 安装APP
- 文件管理器
- 执行脚本
- 计划任务
- 窗口编排
- Gnirehtet（反向网络共享）

## 窗口编排功能

窗口编排功能允许您在一个可视化界面中管理和布局多个设备窗口，实现高效的多设备操作体验。

### 功能特性

- **可视化布局管理**：通过拖拽方式调整设备窗口的位置和大小
- **全局配置支持**：设置适用于所有设备的默认窗口参数
- **设备特定配置**：为每个设备单独配置窗口布局
- **实时预览**：在编排界面中实时查看窗口布局效果
- **布局保存**：保存自定义的窗口布局配置，下次启动时自动应用

### 使用场景

- **多设备同时操作**：需要同时控制多个Android设备时
- **屏幕空间优化**：合理安排设备窗口位置，充分利用显示器空间
- **工作流程标准化**：为特定的工作场景预设窗口布局
- **演示和展示**：在演示或培训时需要展示多个设备界面

### 操作步骤

#### 打开窗口编排

1. 在设备列表中，点击任意设备的"窗口编排"按钮
2. 系统将打开全屏的窗口编排界面

#### 添加设备窗口

1. 点击"添加组件"下拉按钮
2. 选择要添加的设备或"全局配置"
3. 设备窗口将自动添加到编排区域

#### 调整窗口布局

1. **拖拽移动**：点击并拖拽窗口到目标位置
2. **调整大小**：拖拽窗口边角来调整窗口尺寸
3. **实时预览**：所有调整都会实时显示在编排区域

#### 管理窗口组件

- **删除窗口**：点击窗口右上角的删除按钮
- **重置布局**：点击"重置布局"按钮恢复默认设置
- **清除全部**：点击"清除全部"按钮移除所有窗口组件

#### 保存配置

1. 完成窗口布局调整后，点击"保存布局"按钮
2. 系统将保存当前的窗口配置
3. 下次启动设备镜像时，将自动应用保存的布局

### 配置说明

#### 全局配置
- 适用于所有设备的默认窗口参数
- 包括默认窗口大小、位置等设置
- 新添加的设备将继承全局配置

#### 设备特定配置
- 为单个设备设置的专属窗口参数
- 优先级高于全局配置
- 支持为不同设备设置不同的窗口布局

### 注意事项

- 窗口编排功能需要在设备连接后使用
- 建议在较大的显示器上使用此功能以获得更好的体验
- 保存的布局配置会持久化存储，重启应用后仍然有效
- 如果设备断开连接，对应的窗口配置仍会保留