---
title: Operation
---

# Device Operation

## Batch Processing

- Batch mirroring
- Batch Interception Screen
- Batch Installation Application
- Batch File Management
- Batch Execution Script
- Batch Scheduled Task

## Control Model

- Mirror
- Recording
- Recording Camera
- Recording Audio
- Camera
- Custom
- OTG

## Device Interaction Bar

- Switch
- Home
- Back
- Start APP (Mirror Group)
- Turn off screen (experimental)
- Notification
- Power
- Rotation
- Volume
- Screenshot
- Reboot
- Install APP
- File Manager
- Execution Script
- Scheduled Task
- Window Arrangement
- Gnirehtet

## Window Arrangement Feature

The Window Arrangement feature allows you to manage and layout multiple device windows in a visual interface, providing an efficient multi-device operation experience.

### Key Features

- **Visual Layout Management**: Adjust device window positions and sizes through drag-and-drop
- **Global Configuration Support**: Set default window parameters applicable to all devices
- **Device-Specific Configuration**: Configure window layouts individually for each device
- **Real-time Preview**: View window layout effects in real-time within the arrangement interface
- **Layout Persistence**: Save custom window layout configurations that automatically apply on next startup

### Use Cases

- **Multi-device Operations**: When you need to control multiple Android devices simultaneously
- **Screen Space Optimization**: Arrange device windows efficiently to maximize display utilization
- **Workflow Standardization**: Preset window layouts for specific work scenarios
- **Presentation and Demo**: Display multiple device interfaces during presentations or training

### Operating Instructions

#### Opening Window Arrangement

1. Click the "Arrange" button for any device in the device list
2. The system will open a fullscreen window arrangement interface

#### Adding Device Windows

1. Click the "Add Widget" dropdown button
2. Select the device you want to add or "Global Configuration"
3. The device window will be automatically added to the arrangement area

#### Adjusting Window Layout

1. **Drag to Move**: Click and drag windows to target positions
2. **Resize**: Drag window corners to adjust window dimensions
3. **Real-time Preview**: All adjustments are displayed in real-time in the arrangement area

#### Managing Window Components

- **Remove Window**: Click the delete button in the top-right corner of the window
- **Reset Layout**: Click "Reset Layout" button to restore default settings
- **Clear All**: Click "Clear All" button to remove all window components

#### Saving Configuration

1. After completing window layout adjustments, click the "Save Layout" button
2. The system will save the current window configuration
3. The saved layout will automatically apply when starting device mirroring next time

### Configuration Details

#### Global Configuration
- Default window parameters applicable to all devices
- Includes default window size, position, and other settings
- Newly added devices will inherit global configuration

#### Device-Specific Configuration
- Dedicated window parameters set for individual devices
- Takes priority over global configuration
- Supports different window layouts for different devices

### Important Notes

- Window arrangement feature requires connected devices to function
- Recommended to use this feature on larger displays for better experience
- Saved layout configurations are persistently stored and remain effective after application restart
- Device window configurations are retained even if devices disconnect